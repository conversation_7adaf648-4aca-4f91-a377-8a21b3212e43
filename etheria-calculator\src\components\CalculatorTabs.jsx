import { useState } from 'react';
import { motion } from 'framer-motion';
import * as Tabs from '@radix-ui/react-tabs';
import { Zap, Shield, Settings, Users } from 'lucide-react';
import DamageCalculator from './DamageCalculator';
import ShieldCalculator from './ShieldCalculator';
import TeamCollaboration from './TeamCollaboration';

const CalculatorTabs = ({ characterData }) => {
  const [activeTab, setActiveTab] = useState('damage');

  const tabVariants = {
    inactive: { 
      backgroundColor: 'rgba(55, 65, 81, 0.5)',
      color: 'rgba(156, 163, 175, 1)'
    },
    active: { 
      backgroundColor: 'rgba(217, 70, 239, 0.2)',
      color: 'rgba(255, 255, 255, 1)'
    }
  };

  const contentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.3, ease: "easeOut" }
    }
  };

  const TabButton = ({ value, icon: Icon, label, description }) => (
    <Tabs.Trigger value={value} asChild>
      <motion.button
        className="flex-1 p-4 rounded-lg border border-gray-600 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-odd-500"
        variants={tabVariants}
        animate={activeTab === value ? 'active' : 'inactive'}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => setActiveTab(value)}
      >
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${
            activeTab === value 
              ? 'bg-odd-500/30 text-odd-400' 
              : 'bg-gray-700 text-gray-400'
          }`}>
            <Icon className="w-5 h-5" />
          </div>
          <div className="text-left">
            <div className="font-semibold">{label}</div>
            <div className="text-xs opacity-75">{description}</div>
          </div>
        </div>
      </motion.button>
    </Tabs.Trigger>
  );

  return (
    <Tabs.Root value={activeTab} onValueChange={setActiveTab} className="w-full">
      {/* Tab Navigation */}
      <div className="glass rounded-xl p-4 border border-odd-500/30 mb-6">
        <h2 className="text-2xl font-bold text-white mb-4 flex items-center gap-3">
          <span className="w-8 h-8 bg-gradient-to-br from-odd-500 to-purple-600 rounded-lg flex items-center justify-center">
            ⚡
          </span>
          Tiamat Calculator Suite
        </h2>
        
        <Tabs.List className="grid grid-cols-1 md:grid-cols-4 gap-3">
          <TabButton
            value="damage"
            icon={Zap}
            label="Damage Calculator"
            description="Skill damage & DPS"
          />
          <TabButton
            value="shield"
            icon={Shield}
            label="Shield Calculator"
            description="Team protection & sustain"
          />
          <TabButton
            value="builds"
            icon={Settings}
            label="Build Optimizer"
            description="Equipment & modules"
          />
          <TabButton
            value="collaboration"
            icon={Users}
            label="Team Collaboration"
            description="Share & collaborate"
          />
        </Tabs.List>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        variants={contentVariants}
        initial="hidden"
        animate="visible"
      >
        <Tabs.Content value="damage" className="focus:outline-none">
          <DamageCalculator characterData={characterData} />
        </Tabs.Content>

        <Tabs.Content value="shield" className="focus:outline-none">
          <ShieldCalculator characterData={characterData} />
        </Tabs.Content>

        <Tabs.Content value="builds" className="focus:outline-none">
          <div className="glass rounded-xl p-8 border border-gray-500/30 text-center">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <Settings className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-2">Build Optimizer</h3>
              <p className="text-gray-400 mb-6 max-w-md mx-auto">
                Advanced build optimization tools coming soon! Compare different shell and module combinations.
              </p>
              <div className="space-y-3">
                <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                  <div className="w-2 h-2 bg-odd-500 rounded-full animate-pulse"></div>
                  Shell comparison matrix
                </div>
                <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                  Module set optimization
                </div>
                <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                  Stat priority calculator
                </div>
              </div>
            </motion.div>
          </div>
        </Tabs.Content>

        <Tabs.Content value="collaboration" className="focus:outline-none">
          <TeamCollaboration characterData={characterData} />
        </Tabs.Content>
      </motion.div>

      {/* Quick Stats Bar */}
      <motion.div
        className="mt-6 glass rounded-xl p-4 border border-gray-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="text-sm">
              <span className="text-gray-400">Character:</span>
              <span className="text-white font-semibold ml-2">{characterData.name}</span>
            </div>
            <div className="text-sm">
              <span className="text-gray-400">Element:</span>
              <span className="text-odd-400 font-semibold ml-2">{characterData.element}</span>
            </div>
            <div className="text-sm">
              <span className="text-gray-400">Rarity:</span>
              <span className="text-yellow-400 font-semibold ml-2">{characterData.rarity}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-gray-400">Real-time calculations</span>
          </div>
        </div>
      </motion.div>
    </Tabs.Root>
  );
};

export default CalculatorTabs;
