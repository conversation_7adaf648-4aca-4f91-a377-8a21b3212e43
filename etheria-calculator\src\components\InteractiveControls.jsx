import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import * as Tooltip from '@radix-ui/react-tooltip';
import { Info, Copy, Share2, Save, Download, RefreshCw, Zap } from 'lucide-react';

const InteractiveControls = ({ onSave, onShare, onReset, onExport }) => {
  const [showTooltip, setShowTooltip] = useState(null);
  const [isAnimating, setIsAnimating] = useState(false);

  const ControlButton = ({ 
    icon: Icon, 
    label, 
    onClick, 
    color = "odd", 
    tooltip,
    disabled = false 
  }) => (
    <Tooltip.Provider>
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <motion.button
            className={`
              relative p-3 rounded-lg border transition-all duration-300 
              ${disabled 
                ? 'bg-gray-700 border-gray-600 text-gray-500 cursor-not-allowed' 
                : `bg-${color}-500/10 border-${color}-500/30 text-${color}-400 hover:bg-${color}-500/20 hover:border-${color}-500/50`
              }
              focus:outline-none focus:ring-2 focus:ring-${color}-500/50
            `}
            whileHover={!disabled ? { scale: 1.05 } : {}}
            whileTap={!disabled ? { scale: 0.95 } : {}}
            onClick={disabled ? undefined : onClick}
            disabled={disabled}
          >
            <Icon className="w-5 h-5" />
            {isAnimating && (
              <motion.div
                className="absolute inset-0 rounded-lg border-2 border-white opacity-50"
                animate={{ scale: [1, 1.2, 1], opacity: [0.5, 0, 0.5] }}
                transition={{ duration: 0.6 }}
              />
            )}
          </motion.button>
        </Tooltip.Trigger>
        <Tooltip.Portal>
          <Tooltip.Content
            className="bg-gray-800 text-white px-3 py-2 rounded-lg text-sm border border-gray-600 shadow-lg"
            sideOffset={5}
          >
            {tooltip || label}
            <Tooltip.Arrow className="fill-gray-800" />
          </Tooltip.Content>
        </Tooltip.Portal>
      </Tooltip.Root>
    </Tooltip.Provider>
  );

  const QuickPreset = ({ name, stats, description, color = "blue" }) => (
    <motion.div
      className={`p-3 rounded-lg border border-${color}-500/30 bg-${color}-500/10 cursor-pointer transition-all duration-300 hover:bg-${color}-500/20`}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={() => {
        setIsAnimating(true);
        setTimeout(() => setIsAnimating(false), 600);
        // Apply preset logic here
      }}
    >
      <div className="flex items-center justify-between mb-2">
        <h4 className={`font-semibold text-${color}-300`}>{name}</h4>
        <Zap className={`w-4 h-4 text-${color}-400`} />
      </div>
      <p className="text-xs text-gray-400 mb-2">{description}</p>
      <div className="flex gap-2 text-xs">
        <span className="px-2 py-1 bg-gray-700 rounded text-gray-300">
          HP: {stats.hp.toLocaleString()}
        </span>
        <span className="px-2 py-1 bg-gray-700 rounded text-gray-300">
          ATK: {stats.atk}
        </span>
      </div>
    </motion.div>
  );

  const presets = [
    {
      name: "F2P Build",
      stats: { hp: 22815, atk: 943, critRate: 18, critDmg: 150 },
      description: "Base stats with minimal investment",
      color: "green"
    },
    {
      name: "Whale Build",
      stats: { hp: 32000, atk: 1200, critRate: 45, critDmg: 220 },
      description: "Maximum investment build",
      color: "purple"
    },
    {
      name: "PVP Focus",
      stats: { hp: 28000, atk: 1000, critRate: 25, critDmg: 180 },
      description: "Balanced for PVP scenarios",
      color: "red"
    },
    {
      name: "PVE DPS",
      stats: { hp: 25000, atk: 1150, critRate: 40, critDmg: 200 },
      description: "Optimized for PVE damage",
      color: "orange"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Action Controls */}
      <motion.div
        className="glass rounded-xl p-4 border border-gray-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
        
        <div className="flex flex-wrap gap-3">
          <ControlButton
            icon={Save}
            label="Save Build"
            onClick={() => {
              setIsAnimating(true);
              setTimeout(() => setIsAnimating(false), 600);
              onSave?.();
            }}
            color="green"
            tooltip="Save current build configuration"
          />
          
          <ControlButton
            icon={Share2}
            label="Share"
            onClick={() => {
              setIsAnimating(true);
              setTimeout(() => setIsAnimating(false), 600);
              onShare?.();
            }}
            color="blue"
            tooltip="Share build with team members"
          />
          
          <ControlButton
            icon={Copy}
            label="Copy URL"
            onClick={() => {
              navigator.clipboard.writeText(window.location.href);
              setIsAnimating(true);
              setTimeout(() => setIsAnimating(false), 600);
            }}
            color="purple"
            tooltip="Copy shareable URL to clipboard"
          />
          
          <ControlButton
            icon={Download}
            label="Export"
            onClick={() => {
              setIsAnimating(true);
              setTimeout(() => setIsAnimating(false), 600);
              onExport?.();
            }}
            color="yellow"
            tooltip="Export calculations as JSON"
          />
          
          <ControlButton
            icon={RefreshCw}
            label="Reset"
            onClick={() => {
              setIsAnimating(true);
              setTimeout(() => setIsAnimating(false), 600);
              onReset?.();
            }}
            color="red"
            tooltip="Reset to default values"
          />
        </div>
      </motion.div>

      {/* Quick Presets */}
      <motion.div
        className="glass rounded-xl p-4 border border-gray-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <h3 className="text-lg font-semibold text-white mb-4">Quick Presets</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {presets.map((preset, index) => (
            <motion.div
              key={preset.name}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <QuickPreset {...preset} />
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Team Collaboration */}
      <motion.div
        className="glass rounded-xl p-4 border border-blue-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h3 className="text-lg font-semibold text-blue-300 mb-4">Team Collaboration</h3>
        
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
            <div>
              <div className="font-medium text-white">Live Session</div>
              <div className="text-sm text-gray-400">Share calculations in real-time</div>
            </div>
            <motion.button
              className="px-4 py-2 bg-blue-500 text-white rounded-lg font-medium"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => {
                // Start live session logic
                setIsAnimating(true);
                setTimeout(() => setIsAnimating(false), 600);
              }}
            >
              Start Session
            </motion.button>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-green-500/10 rounded-lg border border-green-500/20">
            <div>
              <div className="font-medium text-white">Build Library</div>
              <div className="text-sm text-gray-400">Save and organize team builds</div>
            </div>
            <motion.button
              className="px-4 py-2 bg-green-500 text-white rounded-lg font-medium"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              disabled
            >
              Coming Soon
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* Tips & Info */}
      <motion.div
        className="glass rounded-xl p-4 border border-yellow-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <h3 className="text-lg font-semibold text-yellow-300 mb-4 flex items-center gap-2">
          <Info className="w-5 h-5" />
          Pro Tips
        </h3>
        
        <div className="space-y-2 text-sm text-gray-300">
          <div className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Use the sliders to see real-time damage calculations</span>
          </div>
          <div className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Bloodshed stacks significantly boost shield effectiveness</span>
          </div>
          <div className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Share builds with your team using the copy URL feature</span>
          </div>
          <div className="flex items-start gap-2">
            <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
            <span>Export calculations for spreadsheet analysis</span>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default InteractiveControls;
