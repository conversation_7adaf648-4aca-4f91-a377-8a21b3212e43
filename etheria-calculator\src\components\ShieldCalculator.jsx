import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import * as Slider from '@radix-ui/react-slider';
import { Shield, Heart, TrendingUp, Users, Clock } from 'lucide-react';
import { calculateSkill2Shield, calculateHealingBonus } from '../utils/calculations';

const ShieldCalculator = ({ characterData, onShieldChange }) => {
  const [stats, setStats] = useState(characterData.maxStats);
  const [bloodshedStacks, setBloodshedStacks] = useState(0);
  const [currentHpPercent, setCurrentHpPercent] = useState(100);
  const [skillLevel, setSkillLevel] = useState(5);
  const [teamSize, setTeamSize] = useState(5);
  
  const [shieldData, setShieldData] = useState({});

  useEffect(() => {
    const calculation = calculateSkill2Shield(stats, skillLevel, bloodshedStacks, currentHpPercent);
    setShieldData(calculation);
    
    if (onShieldChange) {
      onShieldChange(calculation);
    }
  }, [stats, bloodshedStacks, currentHpPercent, skillLevel, onShieldChange]);

  const StatSlider = ({ label, value, onChange, min, max, step = 1, icon: Icon, color = "blue", suffix = "" }) => (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Icon className={`w-4 h-4 text-${color}-400`} />
          <span className="text-sm text-gray-300">{label}</span>
        </div>
        <span className="text-white font-semibold">{value.toLocaleString()}{suffix}</span>
      </div>
      <Slider.Root
        className="relative flex items-center select-none touch-none w-full h-5"
        value={[value]}
        onValueChange={([newValue]) => onChange(newValue)}
        min={min}
        max={max}
        step={step}
      >
        <Slider.Track className="bg-gray-700 relative grow rounded-full h-2">
          <Slider.Range className={`absolute bg-gradient-to-r from-${color}-500 to-${color}-400 rounded-full h-full`} />
        </Slider.Track>
        <Slider.Thumb
          className={`block w-4 h-4 bg-${color}-500 shadow-lg rounded-full hover:bg-${color}-400 focus:outline-none focus:ring-2 focus:ring-${color}-400 transition-colors`}
          aria-label={label}
        />
      </Slider.Root>
    </div>
  );

  const ShieldBreakdown = () => {
    const totalTeamShield = shieldData.totalShield * teamSize;
    const totalTeamHealing = shieldData.healOverTime * teamSize * 2; // 2 turns
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Individual Shield */}
        <motion.div
          className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-lg p-4 border border-blue-500/30"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h4 className="text-lg font-semibold text-blue-300 mb-3 flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Per Ally Shield
          </h4>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">HP Consumed:</span>
              <span className="text-red-400 font-semibold">
                {shieldData.hpConsumption?.toLocaleString() || 0}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Base Shield:</span>
              <span className="text-white font-semibold">
                {shieldData.baseShield?.toLocaleString() || 0}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Bloodshed Bonus:</span>
              <span className="text-green-400 font-semibold">
                +{shieldData.bloodshedBonus?.toFixed(1) || 0}%
              </span>
            </div>
            <div className="border-t border-gray-600 pt-2">
              <div className="flex justify-between">
                <span className="text-blue-300 font-semibold">Total Shield:</span>
                <span className="text-blue-400 font-bold text-lg">
                  {shieldData.totalShield?.toLocaleString() || 0}
                </span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Team Shield */}
        <motion.div
          className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-lg p-4 border border-green-500/30"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <h4 className="text-lg font-semibold text-green-300 mb-3 flex items-center gap-2">
            <Users className="w-5 h-5" />
            Team Total
          </h4>
          
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-400">Team Size:</span>
              <span className="text-white font-semibold">{teamSize} allies</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Total Shield:</span>
              <span className="text-green-400 font-semibold">
                {totalTeamShield.toLocaleString()}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">HoT per Turn:</span>
              <span className="text-green-400 font-semibold">
                {(shieldData.healOverTime * teamSize)?.toLocaleString() || 0}
              </span>
            </div>
            <div className="border-t border-gray-600 pt-2">
              <div className="flex justify-between">
                <span className="text-green-300 font-semibold">Total Healing (2T):</span>
                <span className="text-green-400 font-bold text-lg">
                  {totalTeamHealing.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    );
  };

  const EfficiencyMeter = () => {
    const efficiency = Math.min((shieldData.totalShield / stats.hp) * 100, 100);
    
    return (
      <motion.div
        className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-lg p-4 border border-purple-500/30"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h4 className="text-lg font-semibold text-purple-300 mb-3 flex items-center gap-2">
          <TrendingUp className="w-5 h-5" />
          Shield Efficiency
        </h4>
        
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-gray-400">Shield vs Max HP:</span>
            <span className="text-purple-400 font-semibold">{efficiency.toFixed(1)}%</span>
          </div>
          
          <div className="w-full bg-gray-700 rounded-full h-3 overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-purple-500 to-pink-400 rounded-full relative"
              initial={{ width: 0 }}
              animate={{ width: `${efficiency}%` }}
              transition={{ duration: 1, delay: 0.3 }}
            >
              <div className="absolute inset-0 bg-white opacity-30 animate-shimmer"></div>
            </motion.div>
          </div>
          
          <div className="text-xs text-gray-400">
            {efficiency > 80 && "🔥 Excellent shield efficiency!"}
            {efficiency > 60 && efficiency <= 80 && "✨ Good shield value"}
            {efficiency > 40 && efficiency <= 60 && "⚡ Decent protection"}
            {efficiency <= 40 && "💡 Consider increasing HP or Bloodshed stacks"}
          </div>
        </div>
      </motion.div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="glass rounded-xl p-6 border border-blue-500/30">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
          <Shield className="w-5 h-5 text-blue-400" />
          Shield Calculator - Seal of Life
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <StatSlider
            label="Max HP"
            value={stats.hp}
            onChange={(value) => setStats(prev => ({ ...prev, hp: value }))}
            min={15000}
            max={35000}
            step={100}
            icon={Heart}
            color="red"
          />
          <StatSlider
            label="Current HP"
            value={currentHpPercent}
            onChange={setCurrentHpPercent}
            min={1}
            max={100}
            step={1}
            icon={Heart}
            color="green"
            suffix="%"
          />
          <StatSlider
            label="Bloodshed Stacks"
            value={bloodshedStacks}
            onChange={setBloodshedStacks}
            min={0}
            max={20}
            step={1}
            icon={TrendingUp}
            color="red"
          />
          <StatSlider
            label="Skill Level"
            value={skillLevel}
            onChange={setSkillLevel}
            min={1}
            max={5}
            step={1}
            icon={TrendingUp}
            color="purple"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <StatSlider
            label="Team Size"
            value={teamSize}
            onChange={setTeamSize}
            min={1}
            max={5}
            step={1}
            icon={Users}
            color="blue"
          />
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Clock className="w-4 h-4" />
            <span>Shield Duration: 2 turns</span>
          </div>
        </div>
      </div>

      {/* Shield Breakdown */}
      <ShieldBreakdown />

      {/* Efficiency Meter */}
      <EfficiencyMeter />

      {/* Tips */}
      <motion.div
        className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-lg p-4 border border-yellow-500/30"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <h4 className="text-lg font-semibold text-yellow-300 mb-2">💡 Shield Optimization Tips</h4>
        <ul className="text-sm text-gray-300 space-y-1">
          <li>• Use Seal of Life at higher HP for maximum shield value</li>
          <li>• Build Bloodshed stacks before casting for bonus shield strength</li>
          <li>• Pair with HP% modules to increase both damage and shield scaling</li>
          <li>• Time the cast to protect allies before enemy burst phases</li>
        </ul>
      </motion.div>
    </div>
  );
};

export default ShieldCalculator;
