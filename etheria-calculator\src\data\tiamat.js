export const tiamatData = {
  // Basic Character Info
  name: "Tiamat",
  title: "Abyssal Nightmare",
  rarity: "SSR",
  element: "Odd",
  faction: "Leiboz Life",
  
  // Base Stats (Level 50, fully upgraded, no modules/shells)
  baseStats: {
    hp: 16452,
    def: 618,
    atk: 653,
    spd: 117,
    critRate: 10,
    critDmg: 150,
    effectAcc: 0,
    effectRes: 0
  },
  
  // Prowess Stats (gained from upgrades)
  prowessStats: {
    hp: 6363,
    def: 212,
    atk: 290,
    spd: 15,
    critRate: 8,
    critDmg: 0,
    effectAcc: 0,
    effectRes: 20
  },
  
  // Combined Stats (base + prowess)
  maxStats: {
    hp: 22815,
    def: 830,
    atk: 943,
    spd: 132,
    critRate: 18,
    critDmg: 150,
    effectAcc: 0,
    effectRes: 20
  },
  
  // Skills
  skills: {
    skill1: {
      name: "Thunder Spear",
      cooldown: 0,
      tags: ["Single DMG", "Buff"],
      description: "<PERSON><PERSON><PERSON> swings her spear to pierce the target, dealing 2 hit(s) of damage equal to 10% of her Max HP to a single enemy, consuming 8% of her current HP with each hit and adding real damage equal to 100% of the consumed amount.",
      passive: "Mother of Evolution: Takes 25% of damage for other teammates; for every 13% <PERSON> HP lost or healed, gains 1 stack(s) of [Bloodshed]. Upon reaching 8 stacks of [Bloodshed] enters [Ultimate Form], recovers 10% of her maximum HP and immediately gains an [Extra Turn].",
      levels: {
        1: { hpDamagePercent: 10, hpConsumptionPercent: 8, realDamageMultiplier: 100, damageSharing: 25, bloodshedTrigger: 13, bloodshedStacks: 1, ultimateFormStacks: 8, hpRecovery: 10 },
        2: { hpDamagePercent: 10, hpConsumptionPercent: 8, realDamageMultiplier: 100, damageSharing: 25, bloodshedTrigger: 13, bloodshedStacks: 1, ultimateFormStacks: 8, hpRecovery: 10 },
        3: { hpDamagePercent: 10, hpConsumptionPercent: 8, realDamageMultiplier: 100, damageSharing: 25, bloodshedTrigger: 13, bloodshedStacks: 1, ultimateFormStacks: 8, hpRecovery: 10 },
        4: { hpDamagePercent: 10, hpConsumptionPercent: 8, realDamageMultiplier: 100, damageSharing: 25, bloodshedTrigger: 13, bloodshedStacks: 1, ultimateFormStacks: 8, hpRecovery: 10 },
        5: { hpDamagePercent: 10, hpConsumptionPercent: 8, realDamageMultiplier: 100, damageSharing: 25, bloodshedTrigger: 13, bloodshedStacks: 1, ultimateFormStacks: 8, hpRecovery: 10 }
      }
    },
    skill2: {
      name: "Seal of Life",
      cooldown: 3,
      tags: ["Shield", "Heal Over Time"],
      description: "Tiamat establishes a mental bond with allies, consuming 25% of her current HP to apply a 2-turn [Heal Over Time] and [Shield] to all allies. The shield amount equals 120% of HP consumed. For every 1 stack(s) of [Bloodshed] Tiamat has, the shield amount increases by 5.0%.",
      levels: {
        1: { hpConsumptionPercent: 25, shieldMultiplier: 105, bloodshedBonus: 4.0, duration: 2 },
        2: { hpConsumptionPercent: 25, shieldMultiplier: 105, bloodshedBonus: 4.0, duration: 2 },
        3: { hpConsumptionPercent: 25, shieldMultiplier: 105, bloodshedBonus: 4.0, duration: 2 },
        4: { hpConsumptionPercent: 25, shieldMultiplier: 120, bloodshedBonus: 5.0, duration: 2 },
        5: { hpConsumptionPercent: 25, shieldMultiplier: 120, bloodshedBonus: 5.0, duration: 2 }
      }
    },
    skill3: {
      name: "Eternity",
      cooldown: 4,
      tags: ["Taunt", "AoE"],
      description: "Tiamat consumes her own life to empower her spear and throws it to penetrate enemies; consumes 30% of her current HP to deal 2 hit(s) of damage equal to 10% of her Max HP to all enemies, with a 50% chance to inflict a 1-turn [Taunt].",
      levels: {
        1: { hpConsumptionPercent: 30, hpDamagePercent: 10, tauntChance: 50, hits: 2 },
        2: { hpConsumptionPercent: 30, hpDamagePercent: 10, tauntChance: 50, hits: 2 },
        3: { hpConsumptionPercent: 30, hpDamagePercent: 11, tauntChance: 50, hits: 2 },
        4: { hpConsumptionPercent: 30, hpDamagePercent: 11, tauntChance: 50, hits: 2, hpRecovery: 20 },
        5: { hpConsumptionPercent: 30, hpDamagePercent: 13, tauntChance: 50, hits: 2, hpRecovery: 20 }
      }
    },
    ultimateSkill: {
      name: "Purge Day",
      cooldown: 0,
      tags: ["AoE", "Lifesteal"],
      description: "Tiamat unleashes purifying flames to incinerate enemies, dealing damage equal to 15% of her Max HP to all enemies, adding additional true damage equal to 13% of her current HP, and recovering HP equal to 20% of DMG Dealt. This skill does not generate CD.",
      levels: {
        1: { hpDamagePercent: 15, currentHpDamagePercent: 13, lifestealPercent: 20 }
      }
    }
  },
  
  // Status Effects
  statusEffects: {
    bloodshed: {
      name: "Bloodshed",
      description: "Each stack increases healing received by 3%, up to a maximum of 20 stacks, and cannot be Dispelled.",
      maxStacks: 20,
      healingBonus: 3
    },
    ultimateForm: {
      name: "Ultimate Form",
      description: "Takes on the [Ultimate Form], gains and can only use [Purge Day]; loses increasing stacks of [Bloodshed] at the end of each turn",
      stackLoss: {
        turn1: 2,
        turn2: 4,
        turn3: 8,
        turnAfter: 16
      }
    },
    healOverTime: {
      name: "Heal Over Time",
      description: "When the turn starts, recovers 15% of Max HP.",
      healingPercent: 15
    },
    shield: {
      name: "Shield",
      description: "Absorbs an equal amount of damage."
    },
    taunt: {
      name: "Taunt",
      description: "Automatically casts 1 skill(s) on the taunting enemy and cannot take any other action."
    }
  },
  
  // Prowess (Dupes)
  prowess: {
    p1: { name: "Prowess 1", effect: "Increases Basic ATK, DEF and HP by 5%." },
    p2: { name: "Prowess 2", effect: "Healing Received +10%" },
    p3: { name: "Prowess 3", effect: "Increases Basic ATK, DEF and HP by 5%." },
    p4: { name: "Prowess 4", effect: "CRIT Rate +8%" },
    p5: { name: "Prowess 5", effect: "Increases DMG Dealt by an additional 10% and decreases DMG Taken by an additional 10%." }
  },
  
  // Tier Ratings
  ratings: {
    genericPVE: "T1",
    aurora: "T1",
    dokiDoki: "T1",
    terrormaton: "T4",
    unionBoss: "T2",
    pvp: "T0.5"
  }
};

// Equipment and Build Data
export const tiamatBuilds = {
  shells: {
    jackalGuard: {
      name: "Jackal Guard",
      rarity: "Legendary",
      description: "After the main body's turn, a Follow-up Attack on the main target dealing 240%+4200 damage, applies [SPD+] for 3 turns to the main Animus, applies and increases their Turn Meter by 25%.",
      type: "Speed-based DPS",
      modules: {
        set12: ["Swiftrush", "Wellspring", "Bloodbath"],
        set8: ["Swiftrush", "Bulwark", "Cure"],
        set6: ["Swiftsmite", "Evolguard"]
      }
    },
    lighthouse: {
      name: "Lighthouse",
      rarity: "Legendary",
      description: "Triggers when the main body takes DMG greater than 25% of Max HP within a turn, healing the main body for 25% Max HP and increasing Turn Meter progress by 25%.",
      type: "Sustain/Counter",
      modules: {
        set12: ["Swiftrush"],
        set8: ["Bramble"],
        set6: ["Swiftsmite", "Colossguard"]
      }
    },
    alicorn: {
      name: "Alicorn",
      rarity: "Legendary",
      description: "Before the owner takes action, if the owner has any debuffs, dispels 2 debuff(s) and increases their Effect RES by 25% for every 1 debuff(s) dispelled, lasting for 2 turn(s).",
      type: "PVP Focused",
      modules: {
        set12: ["Swiftrush"],
        set8: ["Bramble"],
        set6: ["Swiftsmite", "Colossguard"]
      }
    }
  },
  
  stats: {
    priority: ["HP%", "SPD", "CRIT Rate", "CRIT DMG", "Flat HP"],
    mainStats: {
      slot1: "HP%",
      slot2: "HP% = CRIT Rate",
      slot3: "HP% = CRIT Rate = CRIT DMG"
    },
    substats: "SPD (Until Desired) > HP% > CRIT Rate > CRIT DMG > Flat HP"
  }
};
