// Tiamat Damage and Shield Calculations

/**
 * Calculate Tiamat's Skill 1 (<PERSON> Spear) damage
 * @param {Object} stats - Character stats
 * @param {number} skillLevel - Skill level (1-5)
 * @param {number} bloodshedStacks - Current bloodshed stacks
 * @param {boolean} isUltimateForm - Whether in ultimate form
 * @returns {Object} Damage calculation results
 */
export function calculateSkill1Damage(stats, skillLevel = 5, bloodshedStacks = 0, isUltimateForm = false) {
  const { hp: maxHP, critRate, critDmg } = stats;
  
  // Skill data based on level
  const skillData = {
    hpDamagePercent: 10,
    hpConsumptionPercent: 8,
    realDamageMultiplier: 100,
    hits: 2
  };
  
  // Base damage per hit (10% of Max HP)
  const baseDamagePerHit = maxHP * (skillData.hpDamagePercent / 100);
  
  // HP consumption per hit (8% of current HP)
  const hpConsumptionPerHit = maxHP * (skillData.hpConsumptionPercent / 100);
  
  // Real damage equals 100% of consumed HP
  const realDamagePerHit = hpConsumptionPerHit * (skillData.realDamageMultiplier / 100);
  
  // Total damage per hit
  const totalDamagePerHit = baseDamagePerHit + realDamagePerHit;
  
  // Calculate crit damage
  const critMultiplier = 1 + (critDmg / 100);
  const critDamagePerHit = totalDamagePerHit * critMultiplier;
  
  // Total damage for all hits
  const totalDamage = totalDamagePerHit * skillData.hits;
  const totalCritDamage = critDamagePerHit * skillData.hits;
  const totalHpConsumption = hpConsumptionPerHit * skillData.hits;
  
  return {
    baseDamagePerHit,
    realDamagePerHit,
    totalDamagePerHit,
    critDamagePerHit,
    totalDamage,
    totalCritDamage,
    totalHpConsumption,
    hits: skillData.hits,
    critChance: critRate
  };
}

/**
 * Calculate Tiamat's Skill 2 (Seal of Life) shield amount
 * @param {Object} stats - Character stats
 * @param {number} skillLevel - Skill level (1-5)
 * @param {number} bloodshedStacks - Current bloodshed stacks
 * @param {number} currentHpPercent - Current HP percentage (0-100)
 * @returns {Object} Shield calculation results
 */
export function calculateSkill2Shield(stats, skillLevel = 5, bloodshedStacks = 0, currentHpPercent = 100) {
  const { hp: maxHP } = stats;
  
  // Skill data based on level
  const skillData = {
    1: { hpConsumptionPercent: 25, shieldMultiplier: 105, bloodshedBonus: 4.0 },
    2: { hpConsumptionPercent: 25, shieldMultiplier: 105, bloodshedBonus: 4.0 },
    3: { hpConsumptionPercent: 25, shieldMultiplier: 105, bloodshedBonus: 4.0 },
    4: { hpConsumptionPercent: 25, shieldMultiplier: 120, bloodshedBonus: 5.0 },
    5: { hpConsumptionPercent: 25, shieldMultiplier: 120, bloodshedBonus: 5.0 }
  };
  
  const currentSkillData = skillData[skillLevel];
  
  // Current HP amount
  const currentHP = maxHP * (currentHpPercent / 100);
  
  // HP consumption (25% of current HP)
  const hpConsumption = currentHP * (currentSkillData.hpConsumptionPercent / 100);
  
  // Base shield amount (120% of consumed HP at max level)
  const baseShield = hpConsumption * (currentSkillData.shieldMultiplier / 100);
  
  // Bloodshed bonus (5% per stack at max level)
  const bloodshedBonus = bloodshedStacks * (currentSkillData.bloodshedBonus / 100);
  const bloodshedShieldBonus = baseShield * bloodshedBonus;
  
  // Total shield amount
  const totalShield = baseShield + bloodshedShieldBonus;
  
  return {
    hpConsumption,
    baseShield,
    bloodshedBonus: bloodshedBonus * 100, // Convert to percentage
    bloodshedShieldBonus,
    totalShield,
    duration: 2, // turns
    healOverTime: maxHP * 0.15 // 15% Max HP per turn
  };
}

/**
 * Calculate Tiamat's Skill 3 (Eternity) damage
 * @param {Object} stats - Character stats
 * @param {number} skillLevel - Skill level (1-5)
 * @param {number} currentHpPercent - Current HP percentage (0-100)
 * @param {boolean} isUltimateForm - Whether in ultimate form
 * @returns {Object} Damage calculation results
 */
export function calculateSkill3Damage(stats, skillLevel = 5, currentHpPercent = 100, isUltimateForm = false) {
  const { hp: maxHP, critRate, critDmg } = stats;
  
  // Skill data based on level
  const skillData = {
    1: { hpConsumptionPercent: 30, hpDamagePercent: 10, tauntChance: 50 },
    2: { hpConsumptionPercent: 30, hpDamagePercent: 10, tauntChance: 50 },
    3: { hpConsumptionPercent: 30, hpDamagePercent: 11, tauntChance: 50 },
    4: { hpConsumptionPercent: 30, hpDamagePercent: 11, tauntChance: 50, hpRecovery: 20 },
    5: { hpConsumptionPercent: 30, hpDamagePercent: 13, tauntChance: 50, hpRecovery: 20 }
  };
  
  const currentSkillData = skillData[skillLevel];
  const currentHP = maxHP * (currentHpPercent / 100);
  
  // HP consumption (30% of current HP)
  const hpConsumption = currentHP * (currentSkillData.hpConsumptionPercent / 100);
  
  // Base damage per hit (varies by level, 13% at max level)
  const baseDamagePerHit = maxHP * (currentSkillData.hpDamagePercent / 100);
  
  // Calculate crit damage
  const critMultiplier = 1 + (critDmg / 100);
  const critDamagePerHit = baseDamagePerHit * critMultiplier;
  
  // Total damage for 2 hits
  const totalDamage = baseDamagePerHit * 2;
  const totalCritDamage = critDamagePerHit * 2;
  
  return {
    hpConsumption,
    baseDamagePerHit,
    critDamagePerHit,
    totalDamage,
    totalCritDamage,
    hits: 2,
    tauntChance: currentSkillData.tauntChance,
    hpRecovery: currentSkillData.hpRecovery || 0,
    critChance: critRate
  };
}

/**
 * Calculate Tiamat's Ultimate Skill (Purge Day) damage
 * @param {Object} stats - Character stats
 * @param {number} currentHpPercent - Current HP percentage (0-100)
 * @returns {Object} Damage calculation results
 */
export function calculateUltimateSkillDamage(stats, currentHpPercent = 100) {
  const { hp: maxHP, critRate, critDmg } = stats;
  
  const currentHP = maxHP * (currentHpPercent / 100);
  
  // Base damage (15% of Max HP)
  const baseDamage = maxHP * 0.15;
  
  // Additional true damage (13% of current HP)
  const trueDamage = currentHP * 0.13;
  
  // Total damage
  const totalDamage = baseDamage + trueDamage;
  
  // Calculate crit damage
  const critMultiplier = 1 + (critDmg / 100);
  const critDamage = totalDamage * critMultiplier;
  
  // Lifesteal (20% of damage dealt)
  const lifesteal = totalDamage * 0.20;
  const critLifesteal = critDamage * 0.20;
  
  return {
    baseDamage,
    trueDamage,
    totalDamage,
    critDamage,
    lifesteal,
    critLifesteal,
    critChance: critRate
  };
}

/**
 * Calculate bloodshed stacks gained from HP changes
 * @param {number} maxHP - Maximum HP
 * @param {number} hpChange - HP change amount (positive for healing, negative for damage)
 * @returns {number} Number of bloodshed stacks gained
 */
export function calculateBloodshedStacks(maxHP, hpChange) {
  const hpChangePercent = Math.abs(hpChange) / maxHP * 100;
  const stacksGained = Math.floor(hpChangePercent / 13); // 1 stack per 13% HP change
  return stacksGained;
}

/**
 * Calculate damage sharing from Mother of Evolution passive
 * @param {number} incomingDamage - Damage dealt to ally
 * @returns {number} Damage redirected to Tiamat (25%)
 */
export function calculateDamageSharing(incomingDamage) {
  return incomingDamage * 0.25;
}

/**
 * Calculate healing bonus from bloodshed stacks
 * @param {number} baseHealing - Base healing amount
 * @param {number} bloodshedStacks - Current bloodshed stacks
 * @returns {number} Enhanced healing amount
 */
export function calculateHealingBonus(baseHealing, bloodshedStacks) {
  const healingBonus = Math.min(bloodshedStacks * 3, 60); // 3% per stack, max 20 stacks (60%)
  return baseHealing * (1 + healingBonus / 100);
}
