import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Users, Share2, Save, Download, Upload, Copy, Check, X } from 'lucide-react';

const TeamCollaboration = ({ characterData, currentBuild }) => {
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [sessionId, setSessionId] = useState('');
  const [teamMembers, setTeamMembers] = useState([]);
  const [savedBuilds, setSavedBuilds] = useState([]);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [buildName, setBuildName] = useState('');
  const [copySuccess, setCopySuccess] = useState(false);

  // Mock team members for demonstration
  useEffect(() => {
    setTeamMembers([
      { id: 1, name: '<PERSON>', status: 'online', avatar: '👤', role: 'DPS Specialist' },
      { id: 2, name: '<PERSON>', status: 'online', avatar: '👤', role: 'Support Main' },
      { id: 3, name: 'Sam', status: 'away', avatar: '👤', role: 'Tank Expert' },
      { id: 4, name: 'Casey', status: 'offline', avatar: '👤', role: 'Theorycrafting' },
    ]);

    // Mock saved builds
    setSavedBuilds([
      {
        id: 1,
        name: 'PVP Tiamat',
        author: 'Alex',
        stats: { hp: 28000, atk: 1000, critRate: 25, critDmg: 180 },
        timestamp: '2 hours ago',
        likes: 5
      },
      {
        id: 2,
        name: 'DokiDoki Hell',
        author: 'Jordan',
        stats: { hp: 25000, atk: 1150, critRate: 40, critDmg: 200 },
        timestamp: '1 day ago',
        likes: 12
      },
      {
        id: 3,
        name: 'F2P Friendly',
        author: 'Sam',
        stats: { hp: 22815, atk: 943, critRate: 18, critDmg: 150 },
        timestamp: '3 days ago',
        likes: 8
      }
    ]);
  }, []);

  const startLiveSession = () => {
    const newSessionId = Math.random().toString(36).substring(2, 8).toUpperCase();
    setSessionId(newSessionId);
    setIsSessionActive(true);
  };

  const endLiveSession = () => {
    setIsSessionActive(false);
    setSessionId('');
  };

  const saveBuild = () => {
    if (!buildName.trim()) return;
    
    const newBuild = {
      id: Date.now(),
      name: buildName,
      author: 'You',
      stats: currentBuild || characterData.maxStats,
      timestamp: 'Just now',
      likes: 0
    };
    
    setSavedBuilds(prev => [newBuild, ...prev]);
    setBuildName('');
    setShowSaveModal(false);
  };

  const shareCurrentBuild = async () => {
    const buildData = {
      character: characterData.name,
      stats: currentBuild || characterData.maxStats,
      timestamp: new Date().toISOString()
    };
    
    const shareUrl = `${window.location.origin}?build=${btoa(JSON.stringify(buildData))}`;
    
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const exportBuild = () => {
    const buildData = {
      character: characterData.name,
      stats: currentBuild || characterData.maxStats,
      timestamp: new Date().toISOString(),
      version: '1.0'
    };
    
    const dataStr = JSON.stringify(buildData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${characterData.name}_build_${Date.now()}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  const TeamMember = ({ member }) => (
    <motion.div
      className="flex items-center gap-3 p-3 bg-gray-800/50 rounded-lg border border-gray-700"
      whileHover={{ scale: 1.02 }}
    >
      <div className="relative">
        <div className="w-10 h-10 bg-gradient-to-br from-odd-500 to-purple-600 rounded-full flex items-center justify-center text-white">
          {member.avatar}
        </div>
        <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-gray-800 ${
          member.status === 'online' ? 'bg-green-500' :
          member.status === 'away' ? 'bg-yellow-500' : 'bg-gray-500'
        }`} />
      </div>
      <div className="flex-1">
        <div className="font-medium text-white">{member.name}</div>
        <div className="text-xs text-gray-400">{member.role}</div>
      </div>
      <div className={`text-xs px-2 py-1 rounded-full ${
        member.status === 'online' ? 'bg-green-500/20 text-green-400' :
        member.status === 'away' ? 'bg-yellow-500/20 text-yellow-400' :
        'bg-gray-500/20 text-gray-400'
      }`}>
        {member.status}
      </div>
    </motion.div>
  );

  const BuildCard = ({ build, onLoad }) => (
    <motion.div
      className="p-4 bg-gray-800/50 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors"
      whileHover={{ scale: 1.02 }}
    >
      <div className="flex items-start justify-between mb-3">
        <div>
          <h4 className="font-semibold text-white">{build.name}</h4>
          <p className="text-sm text-gray-400">by {build.author} • {build.timestamp}</p>
        </div>
        <div className="flex items-center gap-1 text-sm text-gray-400">
          <span>❤️</span>
          <span>{build.likes}</span>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-2 text-xs mb-3">
        <div className="bg-gray-700 rounded px-2 py-1">
          <span className="text-gray-400">HP:</span>
          <span className="text-white ml-1">{build.stats.hp.toLocaleString()}</span>
        </div>
        <div className="bg-gray-700 rounded px-2 py-1">
          <span className="text-gray-400">ATK:</span>
          <span className="text-white ml-1">{build.stats.atk}</span>
        </div>
        <div className="bg-gray-700 rounded px-2 py-1">
          <span className="text-gray-400">CRIT:</span>
          <span className="text-white ml-1">{build.stats.critRate}%</span>
        </div>
        <div className="bg-gray-700 rounded px-2 py-1">
          <span className="text-gray-400">CDMG:</span>
          <span className="text-white ml-1">{build.stats.critDmg}%</span>
        </div>
      </div>
      
      <motion.button
        className="w-full py-2 bg-odd-500/20 text-odd-400 rounded border border-odd-500/30 hover:bg-odd-500/30 transition-colors"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={() => onLoad?.(build)}
      >
        Load Build
      </motion.button>
    </motion.div>
  );

  return (
    <div className="space-y-6">
      {/* Live Session */}
      <motion.div
        className="glass rounded-xl p-6 border border-blue-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
          <Users className="w-5 h-5 text-blue-400" />
          Live Collaboration
        </h3>
        
        {!isSessionActive ? (
          <div className="text-center">
            <p className="text-gray-400 mb-4">
              Start a live session to share calculations with your team in real-time
            </p>
            <motion.button
              className="px-6 py-3 bg-blue-500 text-white rounded-lg font-medium"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={startLiveSession}
            >
              Start Live Session
            </motion.button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
              <div>
                <div className="font-medium text-white">Session Active</div>
                <div className="text-sm text-gray-400">Session ID: {sessionId}</div>
              </div>
              <div className="flex gap-2">
                <motion.button
                  className="px-3 py-1 bg-gray-600 text-white rounded text-sm"
                  whileHover={{ scale: 1.05 }}
                  onClick={() => {
                    navigator.clipboard.writeText(sessionId);
                    setCopySuccess(true);
                    setTimeout(() => setCopySuccess(false), 2000);
                  }}
                >
                  {copySuccess ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                </motion.button>
                <motion.button
                  className="px-3 py-1 bg-red-500 text-white rounded text-sm"
                  whileHover={{ scale: 1.05 }}
                  onClick={endLiveSession}
                >
                  End
                </motion.button>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium text-white">Connected Members</h4>
              {teamMembers.filter(m => m.status === 'online').map(member => (
                <TeamMember key={member.id} member={member} />
              ))}
            </div>
          </div>
        )}
      </motion.div>

      {/* Quick Actions */}
      <motion.div
        className="glass rounded-xl p-6 border border-green-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <h3 className="text-xl font-bold text-white mb-4">Quick Actions</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <motion.button
            className="p-3 bg-green-500/20 text-green-400 rounded-lg border border-green-500/30 hover:bg-green-500/30 transition-colors flex flex-col items-center gap-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowSaveModal(true)}
          >
            <Save className="w-5 h-5" />
            <span className="text-sm">Save Build</span>
          </motion.button>
          
          <motion.button
            className="p-3 bg-blue-500/20 text-blue-400 rounded-lg border border-blue-500/30 hover:bg-blue-500/30 transition-colors flex flex-col items-center gap-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={shareCurrentBuild}
          >
            <Share2 className="w-5 h-5" />
            <span className="text-sm">Share</span>
          </motion.button>
          
          <motion.button
            className="p-3 bg-yellow-500/20 text-yellow-400 rounded-lg border border-yellow-500/30 hover:bg-yellow-500/30 transition-colors flex flex-col items-center gap-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={exportBuild}
          >
            <Download className="w-5 h-5" />
            <span className="text-sm">Export</span>
          </motion.button>
          
          <motion.button
            className="p-3 bg-purple-500/20 text-purple-400 rounded-lg border border-purple-500/30 hover:bg-purple-500/30 transition-colors flex flex-col items-center gap-2"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Upload className="w-5 h-5" />
            <span className="text-sm">Import</span>
          </motion.button>
        </div>
      </motion.div>

      {/* Saved Builds */}
      <motion.div
        className="glass rounded-xl p-6 border border-purple-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <h3 className="text-xl font-bold text-white mb-4">Team Build Library</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {savedBuilds.map(build => (
            <BuildCard
              key={build.id}
              build={build}
              onLoad={(build) => console.log('Loading build:', build)}
            />
          ))}
        </div>
      </motion.div>

      {/* Save Modal */}
      <AnimatePresence>
        {showSaveModal && (
          <motion.div
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowSaveModal(false)}
          >
            <motion.div
              className="bg-gray-800 rounded-xl p-6 border border-gray-600 max-w-md w-full mx-4"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-xl font-bold text-white mb-4">Save Build</h3>
              <input
                type="text"
                placeholder="Enter build name..."
                value={buildName}
                onChange={(e) => setBuildName(e.target.value)}
                className="w-full p-3 bg-gray-700 text-white rounded-lg border border-gray-600 focus:border-odd-500 focus:outline-none mb-4"
                autoFocus
              />
              <div className="flex gap-3">
                <motion.button
                  className="flex-1 py-2 bg-gray-600 text-white rounded-lg"
                  whileHover={{ scale: 1.02 }}
                  onClick={() => setShowSaveModal(false)}
                >
                  Cancel
                </motion.button>
                <motion.button
                  className="flex-1 py-2 bg-green-500 text-white rounded-lg"
                  whileHover={{ scale: 1.02 }}
                  onClick={saveBuild}
                  disabled={!buildName.trim()}
                >
                  Save
                </motion.button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default TeamCollaboration;
