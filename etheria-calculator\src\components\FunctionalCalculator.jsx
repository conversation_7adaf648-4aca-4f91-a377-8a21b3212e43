import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Heart, Shield, Zap, Star, Swords, TrendingUp } from 'lucide-react';
import { 
  calculateSkill1Damage, 
  calculateSkill2Shield, 
  calculateSkill3Damage, 
  calculateUltimateSkillDamage 
} from '../utils/calculations';

const FunctionalCalculator = () => {
  // Character stats that users can input
  const [characterStats, setCharacterStats] = useState({
    hp: 22815,
    atk: 943,
    def: 830,
    spd: 132,
    critRate: 18,
    critDmg: 150,
    effectAcc: 0,
    effectRes: 20
  });

  // Battle conditions
  const [battleConditions, setBattleConditions] = useState({
    currentHpPercent: 100,
    bloodshedStacks: 0,
    skillLevels: {
      skill1: 5,
      skill2: 5,
      skill3: 5
    }
  });

  // Calculated results
  const [calculations, setCalculations] = useState({});

  // Recalculate when inputs change
  useEffect(() => {
    const skill1 = calculateSkill1Damage(
      characterStats, 
      battleConditions.skillLevels.skill1, 
      battleConditions.bloodshedStacks
    );
    
    const skill2 = calculateSkill2Shield(
      characterStats, 
      battleConditions.skillLevels.skill2, 
      battleConditions.bloodshedStacks, 
      battleConditions.currentHpPercent
    );
    
    const skill3 = calculateSkill3Damage(
      characterStats, 
      battleConditions.skillLevels.skill3, 
      battleConditions.currentHpPercent
    );
    
    const ultimate = calculateUltimateSkillDamage(
      characterStats, 
      battleConditions.currentHpPercent
    );

    setCalculations({ skill1, skill2, skill3, ultimate });
  }, [characterStats, battleConditions]);

  const handleStatChange = (statName, value) => {
    const numValue = parseInt(value) || 0;
    setCharacterStats(prev => ({
      ...prev,
      [statName]: numValue
    }));
  };

  const handleConditionChange = (conditionName, value) => {
    const numValue = parseInt(value) || 0;
    setBattleConditions(prev => ({
      ...prev,
      [conditionName]: numValue
    }));
  };

  const StatInput = ({ label, value, onChange, icon: Icon, color = "blue", min = 0, max = 99999 }) => (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <Icon className={`w-4 h-4 text-${color}-400`} />
        <label className="text-sm text-gray-300 font-medium">{label}:</label>
      </div>
      <input
        type="number"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        min={min}
        max={max}
        className="w-full px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
        placeholder="Enter value"
      />
    </div>
  );

  const SkillResult = ({ title, description, results, color = "blue" }) => (
    <motion.div
      className={`p-4 bg-gradient-to-r from-${color}-500/10 to-${color}-600/10 rounded-lg border border-${color}-500/30`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <h4 className={`text-lg font-semibold text-${color}-300 mb-2`}>{title}</h4>
      <p className="text-sm text-gray-400 mb-3">{description}</p>
      
      <div className="space-y-2">
        {Object.entries(results || {}).map(([key, value]) => (
          <div key={key} className="flex justify-between items-center">
            <span className="text-sm text-gray-300 capitalize">
              {key.replace(/([A-Z])/g, ' $1').trim()}:
            </span>
            <span className={`text-sm font-semibold text-${color}-300`}>
              {typeof value === 'number' ? value.toLocaleString() : value}
            </span>
          </div>
        ))}
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-700 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-white mb-2">
            Etheria Restart Calculator
          </h1>
          <p className="text-gray-400">
            Interactive damage and shield calculator for your favorite characters
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Character Input */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {/* Character Card */}
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-purple-300 mb-1">Tiamat</h2>
                <p className="text-gray-400">Tank/DPS - Abyssal Nightmare</p>
              </div>

              {/* Character Stats Input */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-white mb-4">Character Stats</h3>
                
                <div className="grid grid-cols-2 gap-4">
                  <StatInput
                    label="HP"
                    value={characterStats.hp}
                    onChange={(value) => handleStatChange('hp', value)}
                    icon={Heart}
                    color="red"
                    max={50000}
                  />
                  <StatInput
                    label="ATK"
                    value={characterStats.atk}
                    onChange={(value) => handleStatChange('atk', value)}
                    icon={Swords}
                    color="orange"
                    max={3000}
                  />
                  <StatInput
                    label="DEF"
                    value={characterStats.def}
                    onChange={(value) => handleStatChange('def', value)}
                    icon={Shield}
                    color="blue"
                    max={2000}
                  />
                  <StatInput
                    label="SPD"
                    value={characterStats.spd}
                    onChange={(value) => handleStatChange('spd', value)}
                    icon={Zap}
                    color="green"
                    max={300}
                  />
                  <StatInput
                    label="CRIT Rate (%)"
                    value={characterStats.critRate}
                    onChange={(value) => handleStatChange('critRate', value)}
                    icon={Star}
                    color="yellow"
                    max={100}
                  />
                  <StatInput
                    label="CRIT DMG (%)"
                    value={characterStats.critDmg}
                    onChange={(value) => handleStatChange('critDmg', value)}
                    icon={TrendingUp}
                    color="purple"
                    max={500}
                  />
                </div>
              </div>

              {/* Battle Conditions */}
              <div className="mt-6 pt-6 border-t border-gray-700">
                <h3 className="text-lg font-semibold text-white mb-4">Battle Conditions</h3>
                
                <div className="grid grid-cols-2 gap-4">
                  <StatInput
                    label="Current HP (%)"
                    value={battleConditions.currentHpPercent}
                    onChange={(value) => handleConditionChange('currentHpPercent', value)}
                    icon={Heart}
                    color="red"
                    min={1}
                    max={100}
                  />
                  <StatInput
                    label="Bloodshed Stacks"
                    value={battleConditions.bloodshedStacks}
                    onChange={(value) => handleConditionChange('bloodshedStacks', value)}
                    icon={Zap}
                    color="red"
                    min={0}
                    max={8}
                  />
                </div>
              </div>
            </div>
          </motion.div>

          {/* Right Panel - Calculation Results */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <SkillResult
              title="Skill 1: Thunder Spear"
              description="2 hits of 10% Max HP damage. Consumes 8% current HP per hit, adds 100% of consumed HP as real damage."
              results={calculations.skill1}
              color="purple"
            />

            <SkillResult
              title="Skill 2: Seal of Life"
              description="Consumes 25% current HP. Shield = 120% of consumed HP + 5% per Bloodshed stack. Applies Heal Over Time."
              results={calculations.skill2}
              color="green"
            />

            <SkillResult
              title="Skill 3: Eternity"
              description="Deals 3 hits of 10% Max HP damage. Each hit triggers Moonlit Veil for ally with lowest HP."
              results={calculations.skill3}
              color="blue"
            />

            <SkillResult
              title="Ultimate: Purge Day"
              description="Ultimate skill with enhanced damage based on current HP and Bloodshed stacks."
              results={calculations.ultimate}
              color="red"
            />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default FunctionalCalculator;
