import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect } from 'react';

const SkillEffects = ({ skillName, isActive, onComplete }) => {
  const [showEffect, setShowEffect] = useState(false);

  useEffect(() => {
    if (isActive) {
      setShowEffect(true);
      const timer = setTimeout(() => {
        setShowEffect(false);
        onComplete?.();
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [isActive, onComplete]);

  const ThunderSpearEffect = () => (
    <motion.div
      className="absolute inset-0 pointer-events-none"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {/* Lightning bolts */}
      {Array.from({ length: 5 }, (_, i) => (
        <motion.div
          key={i}
          className="absolute w-1 bg-gradient-to-b from-transparent via-odd-400 to-transparent"
          style={{
            height: '200px',
            left: `${20 + i * 15}%`,
            top: '20%',
            transformOrigin: 'top',
          }}
          initial={{ scaleY: 0, opacity: 0 }}
          animate={{ 
            scaleY: [0, 1, 0.8, 1, 0],
            opacity: [0, 1, 0.8, 1, 0],
            rotate: [0, 5, -5, 0]
          }}
          transition={{ 
            duration: 0.8,
            delay: i * 0.1,
            ease: "easeInOut"
          }}
        />
      ))}
      
      {/* Electric particles */}
      {Array.from({ length: 20 }, (_, i) => (
        <motion.div
          key={`particle-${i}`}
          className="absolute w-2 h-2 bg-odd-400 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          initial={{ scale: 0, opacity: 0 }}
          animate={{
            scale: [0, 1, 0],
            opacity: [0, 1, 0],
            x: [0, (Math.random() - 0.5) * 100],
            y: [0, (Math.random() - 0.5) * 100],
          }}
          transition={{
            duration: 1.5,
            delay: Math.random() * 0.5,
            ease: "easeOut"
          }}
        />
      ))}
      
      {/* Shockwave */}
      <motion.div
        className="absolute top-1/2 left-1/2 border-2 border-odd-400 rounded-full"
        style={{ transform: 'translate(-50%, -50%)' }}
        initial={{ width: 0, height: 0, opacity: 1 }}
        animate={{ 
          width: 400, 
          height: 400, 
          opacity: [1, 0.5, 0] 
        }}
        transition={{ duration: 1.2, ease: "easeOut" }}
      />
    </motion.div>
  );

  const SealOfLifeEffect = () => (
    <motion.div
      className="absolute inset-0 pointer-events-none"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {/* Healing aura */}
      <motion.div
        className="absolute top-1/2 left-1/2 rounded-full"
        style={{ 
          transform: 'translate(-50%, -50%)',
          background: 'radial-gradient(circle, rgba(34, 197, 94, 0.3) 0%, rgba(34, 197, 94, 0.1) 50%, transparent 70%)'
        }}
        initial={{ width: 0, height: 0 }}
        animate={{ width: 500, height: 500 }}
        transition={{ duration: 1.5, ease: "easeOut" }}
      />
      
      {/* Shield particles */}
      {Array.from({ length: 12 }, (_, i) => {
        const angle = (i / 12) * Math.PI * 2;
        const radius = 150;
        return (
          <motion.div
            key={i}
            className="absolute w-3 h-3 bg-blue-400 rounded-full"
            style={{
              left: '50%',
              top: '50%',
            }}
            initial={{ 
              x: 0, 
              y: 0, 
              scale: 0,
              opacity: 0 
            }}
            animate={{
              x: Math.cos(angle) * radius,
              y: Math.sin(angle) * radius,
              scale: [0, 1, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 2,
              delay: i * 0.1,
              ease: "easeOut"
            }}
          />
        );
      })}
      
      {/* Healing cross */}
      <motion.div
        className="absolute top-1/2 left-1/2 w-8 h-8"
        style={{ transform: 'translate(-50%, -50%)' }}
        initial={{ scale: 0, rotate: 0 }}
        animate={{ 
          scale: [0, 2, 1], 
          rotate: [0, 180, 360] 
        }}
        transition={{ duration: 1.5, ease: "easeInOut" }}
      >
        <div className="w-full h-1 bg-green-400 absolute top-1/2 transform -translate-y-1/2"></div>
        <div className="h-full w-1 bg-green-400 absolute left-1/2 transform -translate-x-1/2"></div>
      </motion.div>
    </motion.div>
  );

  const EternityEffect = () => (
    <motion.div
      className="absolute inset-0 pointer-events-none"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {/* Dark energy waves */}
      {Array.from({ length: 3 }, (_, i) => (
        <motion.div
          key={i}
          className="absolute top-1/2 left-1/2 border-2 border-red-500 rounded-full"
          style={{ transform: 'translate(-50%, -50%)' }}
          initial={{ width: 0, height: 0, opacity: 1 }}
          animate={{ 
            width: 300 + i * 100, 
            height: 300 + i * 100, 
            opacity: [1, 0.3, 0] 
          }}
          transition={{ 
            duration: 1.5, 
            delay: i * 0.3,
            ease: "easeOut" 
          }}
        />
      ))}
      
      {/* Spear trajectory */}
      <motion.div
        className="absolute w-2 h-20 bg-gradient-to-b from-red-500 to-transparent"
        style={{
          left: '10%',
          top: '20%',
          transformOrigin: 'bottom',
        }}
        initial={{ 
          rotate: 45,
          x: 0,
          y: 0,
          opacity: 0 
        }}
        animate={{
          rotate: [45, 0, -45],
          x: [0, 300, 600],
          y: [0, 100, 200],
          opacity: [0, 1, 0],
        }}
        transition={{ duration: 1.2, ease: "easeInOut" }}
      />
      
      {/* Impact explosion */}
      <motion.div
        className="absolute"
        style={{ left: '70%', top: '60%' }}
        initial={{ scale: 0 }}
        animate={{ scale: [0, 2, 0] }}
        transition={{ duration: 0.8, delay: 1 }}
      >
        {Array.from({ length: 8 }, (_, i) => {
          const angle = (i / 8) * Math.PI * 2;
          return (
            <motion.div
              key={i}
              className="absolute w-1 h-12 bg-red-400"
              style={{
                transformOrigin: 'bottom',
                transform: `rotate(${angle * 180 / Math.PI}deg)`,
              }}
              initial={{ scaleY: 0 }}
              animate={{ scaleY: [0, 1, 0] }}
              transition={{ 
                duration: 0.6, 
                delay: 1 + i * 0.05,
                ease: "easeOut" 
              }}
            />
          );
        })}
      </motion.div>
    </motion.div>
  );

  const PurgeDayEffect = () => (
    <motion.div
      className="absolute inset-0 pointer-events-none"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {/* Purifying flames */}
      <motion.div
        className="absolute inset-0"
        style={{
          background: 'radial-gradient(circle, rgba(147, 51, 234, 0.3) 0%, rgba(217, 70, 239, 0.2) 50%, transparent 70%)'
        }}
        initial={{ scale: 0 }}
        animate={{ scale: [0, 1.5, 1] }}
        transition={{ duration: 2, ease: "easeInOut" }}
      />
      
      {/* Flame particles */}
      {Array.from({ length: 30 }, (_, i) => (
        <motion.div
          key={i}
          className="absolute w-3 h-6 bg-gradient-to-t from-purple-500 to-pink-400 rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            bottom: '0%',
          }}
          initial={{ y: 0, opacity: 0, scale: 0 }}
          animate={{
            y: -200 - Math.random() * 200,
            opacity: [0, 1, 0],
            scale: [0, 1, 0],
            x: (Math.random() - 0.5) * 100,
          }}
          transition={{
            duration: 2,
            delay: Math.random() * 0.5,
            ease: "easeOut"
          }}
        />
      ))}
      
      {/* Central vortex */}
      <motion.div
        className="absolute top-1/2 left-1/2 w-32 h-32 border-4 border-purple-400 rounded-full"
        style={{ transform: 'translate(-50%, -50%)' }}
        initial={{ scale: 0, rotate: 0 }}
        animate={{ 
          scale: [0, 1, 1.5, 0],
          rotate: [0, 360, 720, 1080]
        }}
        transition={{ duration: 2, ease: "easeInOut" }}
      />
    </motion.div>
  );

  const getEffect = () => {
    switch (skillName) {
      case 'Thunder Spear':
        return <ThunderSpearEffect />;
      case 'Seal of Life':
        return <SealOfLifeEffect />;
      case 'Eternity':
        return <EternityEffect />;
      case 'Purge Day':
        return <PurgeDayEffect />;
      default:
        return null;
    }
  };

  return (
    <AnimatePresence>
      {showEffect && (
        <motion.div
          className="fixed inset-0 pointer-events-none z-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
        >
          {getEffect()}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SkillEffects;
