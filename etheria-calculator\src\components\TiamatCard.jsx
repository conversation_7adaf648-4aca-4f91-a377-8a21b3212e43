import { motion } from 'framer-motion';
import { useState } from 'react';
import { <PERSON>, <PERSON>, Zap, Heart, Swords } from 'lucide-react';

const TiamatCard = ({ characterData, onStatsChange }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [selectedTab, setSelectedTab] = useState('stats');
  const [selectedSkill, setSelectedSkill] = useState(null);

  const cardVariants = {
    initial: { 
      scale: 1,
      rotateY: 0,
      boxShadow: "0 10px 30px rgba(217, 70, 239, 0.3)"
    },
    hover: {
      scale: 1.02,
      rotateY: 2,
      boxShadow: "0 20px 50px rgba(217, 70, 239, 0.6), 0 0 30px rgba(217, 70, 239, 0.3)"
    }
  };

  const glowVariants = {
    initial: { opacity: 0.5 },
    animate: { 
      opacity: [0.5, 1, 0.5],
      transition: { duration: 2, repeat: Infinity }
    }
  };

  const StatBar = ({ label, value, maxValue, color = "odd-500" }) => (
    <div className="mb-3">
      <div className="flex justify-between text-sm mb-1">
        <span className="text-gray-300">{label}</span>
        <span className="text-white font-semibold">{value.toLocaleString()}</span>
      </div>
      <div className="w-full bg-gray-700 rounded-full h-2 overflow-hidden">
        <motion.div
          className={`h-full bg-gradient-to-r from-${color} to-purple-400 rounded-full relative`}
          initial={{ width: 0 }}
          animate={{ width: `${(value / maxValue) * 100}%` }}
          transition={{ duration: 1, delay: 0.2 }}
        >
          <div className="absolute inset-0 bg-white opacity-30 animate-shimmer"></div>
        </motion.div>
      </div>
    </div>
  );

  const SkillIcon = ({ skill, isActive, onClick }) => (
    <motion.div
      className={`relative p-3 rounded-lg cursor-pointer transition-all duration-300 ${
        isActive 
          ? 'bg-gradient-to-br from-odd-500 to-purple-600 shadow-lg shadow-odd-500/50' 
          : 'bg-gray-700 hover:bg-gray-600'
      }`}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
    >
      <div className="text-white text-xl">
        {skill.name === 'Thunder Spear' && <Zap />}
        {skill.name === 'Seal of Life' && <Shield />}
        {skill.name === 'Eternity' && <Swords />}
        {skill.name === 'Purge Day' && <Star />}
      </div>
      {isActive && (
        <motion.div
          className="absolute inset-0 rounded-lg border-2 border-white opacity-50"
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 1, repeat: Infinity }}
        />
      )}
    </motion.div>
  );

  return (
    <motion.div
      className="relative w-full max-w-sm mx-auto"
      variants={cardVariants}
      initial="initial"
      animate={isHovered ? "hover" : "initial"}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      {/* Animated background glow */}
      <motion.div
        className="absolute inset-0 rounded-2xl bg-gradient-to-br from-odd-500/20 to-purple-600/20 blur-xl"
        variants={glowVariants}
        initial="initial"
        animate="animate"
      />
      
      {/* Main card */}
      <div className="relative glass rounded-2xl p-6 border border-odd-500/30 overflow-hidden shadow-2xl bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-xl">
        {/* Card Header with Character Portrait */}
        <div className="relative mb-6">
          <div className="flex items-center gap-4">
            <div className="relative">
              <div className="w-16 h-16 rounded-full bg-gradient-to-br from-odd-500 to-purple-600 p-0.5">
                <div className="w-full h-full rounded-full bg-gray-800 flex items-center justify-center">
                  <span className="text-2xl font-bold text-odd-300">T</span>
                </div>
              </div>
              <motion.div
                className="absolute -inset-1 rounded-full border-2 border-odd-500/50"
                animate={{ rotate: 360 }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">{characterData.name}</h2>
              <p className="text-sm text-odd-300">{characterData.element} • {characterData.rarity}★</p>
            </div>
          </div>
        </div>
        {/* Floating particles */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-odd-400 rounded-full"
              animate={{
                x: [0, 100, 0],
                y: [0, -50, 0],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: 3 + i,
                repeat: Infinity,
                delay: i * 0.5
              }}
              style={{
                left: `${20 + i * 15}%`,
                top: `${30 + i * 10}%`
              }}
            />
          ))}
        </div>

        {/* Character header */}
        <div className="relative z-10 text-center mb-6">
          <div className="relative mb-4">
            <motion.div
              className="w-24 h-24 mx-auto rounded-full bg-gradient-to-br from-odd-500 to-purple-600 p-1 shadow-lg shadow-odd-500/50"
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            >
              <div className="w-full h-full rounded-full bg-gray-800 flex items-center justify-center text-3xl relative overflow-hidden">
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"
                  animate={{ x: [-100, 100] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                />
                ⚡
              </div>
            </motion.div>

            {/* Floating orbs around portrait */}
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-odd-400 rounded-full"
                style={{
                  left: '50%',
                  top: '50%',
                }}
                animate={{
                  x: [0, Math.cos((i * 120) * Math.PI / 180) * 50],
                  y: [0, Math.sin((i * 120) * Math.PI / 180) * 50],
                  opacity: [0, 1, 0],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.7,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>

          <h2 className="text-2xl font-bold gradient-text mb-1">
            {characterData.name}
          </h2>
          <p className="text-odd-300 text-sm mb-2">{characterData.title}</p>

          <div className="flex justify-center items-center gap-2 text-xs">
            <motion.span
              className="px-2 py-1 bg-yellow-500/20 text-yellow-300 rounded-full border border-yellow-500/30"
              whileHover={{ scale: 1.1, boxShadow: "0 0 10px rgba(234, 179, 8, 0.5)" }}
            >
              {characterData.rarity}★
            </motion.span>
            <motion.span
              className="px-2 py-1 bg-odd-500/20 text-odd-300 rounded-full border border-odd-500/30"
              whileHover={{ scale: 1.1, boxShadow: "0 0 10px rgba(217, 70, 239, 0.5)" }}
            >
              {characterData.element}
            </motion.span>
            <motion.span
              className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full border border-blue-500/30"
              whileHover={{ scale: 1.1, boxShadow: "0 0 10px rgba(59, 130, 246, 0.5)" }}
            >
              {characterData.faction}
            </motion.span>
          </div>
        </div>

        {/* Tab navigation */}
        <div className="flex justify-center mb-6">
          <div className="relative flex bg-gray-800/50 rounded-lg p-1 border border-gray-700/50">
            {[
              { id: 'stats', label: 'Stats', icon: BarChart3 },
              { id: 'skills', label: 'Skills', icon: Zap }
            ].map((tab) => (
              <motion.button
                key={tab.id}
                className={`relative flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-300 ${
                  selectedTab === tab.id
                    ? 'bg-gradient-to-r from-odd-500 to-purple-600 text-white shadow-lg shadow-odd-500/25'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                }`}
                onClick={() => setSelectedTab(tab.id)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <tab.icon className="w-4 h-4" />
                {tab.label}
                {selectedTab === tab.id && (
                  <motion.div
                    className="absolute inset-0 rounded-md border border-white/20"
                    layoutId="activeTab"
                    transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  />
                )}
              </motion.button>
            ))}
          </div>
        </div>

        {/* Content based on selected tab */}
        <motion.div
          key={selectedTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {selectedTab === 'stats' && (
            <div className="space-y-2">
              <StatBar 
                label="HP" 
                value={characterData.maxStats.hp} 
                maxValue={30000}
                color="red"
              />
              <StatBar 
                label="ATK" 
                value={characterData.maxStats.atk} 
                maxValue={1500}
                color="orange"
              />
              <StatBar 
                label="DEF" 
                value={characterData.maxStats.def} 
                maxValue={1200}
                color="blue"
              />
              <StatBar 
                label="SPD" 
                value={characterData.maxStats.spd} 
                maxValue={200}
                color="green"
              />
              <StatBar 
                label="CRIT Rate" 
                value={characterData.maxStats.critRate} 
                maxValue={100}
                color="yellow"
              />
              <StatBar 
                label="CRIT DMG" 
                value={characterData.maxStats.critDmg} 
                maxValue={300}
                color="purple"
              />
            </div>
          )}

          {selectedTab === 'skills' && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-3">
                {Object.entries(characterData.skills).map(([key, skill]) => (
                  <SkillIcon
                    key={key}
                    skill={skill}
                    isActive={selectedSkill === key}
                    onClick={() => {
                      setSelectedSkill(selectedSkill === key ? null : key);
                      console.log(`Selected ${skill.name}`);
                    }}
                  />
                ))}
              </div>

              {/* Selected Skill Description */}
              {selectedSkill && (
                <motion.div
                  className="mt-4 p-4 bg-gradient-to-r from-odd-500/20 to-purple-500/20 rounded-lg border border-odd-500/30"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <h4 className="text-sm font-semibold text-odd-300 mb-2">
                    {characterData.skills[selectedSkill].name}
                  </h4>
                  <p className="text-xs text-gray-300 leading-relaxed mb-2">
                    {characterData.skills[selectedSkill].description}
                  </p>
                  <div className="text-xs text-odd-400">
                    <span className="font-medium">Cooldown:</span> {characterData.skills[selectedSkill].cooldown}s
                  </div>
                </motion.div>
              )}

              {/* Passive Skill */}
              <div className="mt-4 p-3 bg-gray-800/50 rounded-lg">
                <h4 className="text-sm font-semibold text-odd-300 mb-2">
                  Mother of Evolution (Passive)
                </h4>
                <p className="text-xs text-gray-400 leading-relaxed">
                  Takes 25% of damage for teammates. Gains Bloodshed stacks and enters Ultimate Form at 8 stacks.
                </p>
              </div>
            </div>
          )}
        </motion.div>

        {/* Bloodshed stacks indicator */}
        <div className="mt-6 pt-4 border-t border-gray-700">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-400">Bloodshed Stacks</span>
            <div className="flex gap-1">
              {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  className={`w-2 h-2 rounded-full ${
                    i < 3 ? 'bg-red-500' : 'bg-gray-600'
                  }`}
                  animate={i < 3 ? { scale: [1, 1.2, 1] } : {}}
                  transition={{ duration: 1, repeat: Infinity, delay: i * 0.1 }}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default TiamatCard;
