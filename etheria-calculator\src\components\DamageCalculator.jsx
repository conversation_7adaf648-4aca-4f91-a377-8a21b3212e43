import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import * as Slider from '@radix-ui/react-slider';
import { Zap, Shield, Swords, Star, TrendingUp, Heart } from 'lucide-react';
import { 
  calculateSkill1Damage, 
  calculateSkill2Shield, 
  calculateSkill3Damage, 
  calculateUltimateSkillDamage 
} from '../utils/calculations';

const DamageCalculator = ({ characterData }) => {
  const [stats, setStats] = useState(characterData.maxStats);
  const [bloodshedStacks, setBloodshedStacks] = useState(0);
  const [currentHpPercent, setCurrentHpPercent] = useState(100);
  const [skillLevels, setSkillLevels] = useState({
    skill1: 5,
    skill2: 5,
    skill3: 5
  });
  const [selectedSkill, setSelectedSkill] = useState('skill1');

  // Calculate damage/shield values
  const [calculations, setCalculations] = useState({});

  useEffect(() => {
    const skill1 = calculateSkill1Damage(stats, skillLevels.skill1, bloodshedStacks);
    const skill2 = calculateSkill2Shield(stats, skillLevels.skill2, bloodshedStacks, currentHpPercent);
    const skill3 = calculateSkill3Damage(stats, skillLevels.skill3, currentHpPercent);
    const ultimate = calculateUltimateSkillDamage(stats, currentHpPercent);

    setCalculations({ skill1, skill2, skill3, ultimate });
  }, [stats, bloodshedStacks, currentHpPercent, skillLevels]);

  const StatSlider = ({ label, value, onChange, min, max, step = 1, icon: Icon, color = "odd" }) => (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Icon className={`w-4 h-4 text-${color}-400`} />
          <span className="text-sm text-gray-300">{label}</span>
        </div>
        <span className="text-white font-semibold">{value.toLocaleString()}</span>
      </div>
      <Slider.Root
        className="relative flex items-center select-none touch-none w-full h-5"
        value={[value]}
        onValueChange={([newValue]) => onChange(newValue)}
        min={min}
        max={max}
        step={step}
      >
        <Slider.Track className="bg-gray-700 relative grow rounded-full h-2">
          <Slider.Range className={`absolute bg-gradient-to-r from-${color}-500 to-${color}-400 rounded-full h-full`} />
        </Slider.Track>
        <Slider.Thumb
          className={`block w-4 h-4 bg-${color}-500 shadow-lg rounded-full hover:bg-${color}-400 focus:outline-none focus:ring-2 focus:ring-${color}-400 transition-colors`}
          aria-label={label}
        />
      </Slider.Root>
    </div>
  );

  const SkillCard = ({ skillKey, skill, calculation, isSelected, onClick }) => {
    const icons = {
      skill1: Zap,
      skill2: Shield,
      skill3: Swords,
      ultimate: Star
    };
    const Icon = icons[skillKey];
    
    const colors = {
      skill1: "odd",
      skill2: "blue",
      skill3: "red",
      ultimate: "purple"
    };
    const color = colors[skillKey];

    return (
      <motion.div
        className={`p-4 rounded-lg cursor-pointer transition-all duration-300 ${
          isSelected 
            ? `bg-gradient-to-br from-${color}-500/20 to-${color}-600/20 border-2 border-${color}-500/50` 
            : 'bg-gray-800/50 border border-gray-700 hover:border-gray-600'
        }`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        onClick={onClick}
      >
        <div className="flex items-center gap-3 mb-3">
          <div className={`p-2 rounded-lg bg-${color}-500/20`}>
            <Icon className={`w-5 h-5 text-${color}-400`} />
          </div>
          <div>
            <h3 className="font-semibold text-white">{skill.name}</h3>
            <p className="text-xs text-gray-400">CD: {skill.cooldown}</p>
          </div>
        </div>
        
        {calculation && (
          <div className="space-y-2 text-sm">
            {skillKey === 'skill1' && (
              <>
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Damage:</span>
                  <span className={`text-${color}-400 font-semibold`}>
                    {calculation.totalDamage.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Crit Damage:</span>
                  <span className="text-yellow-400 font-semibold">
                    {calculation.totalCritDamage.toLocaleString()}
                  </span>
                </div>
              </>
            )}
            
            {skillKey === 'skill2' && (
              <>
                <div className="flex justify-between">
                  <span className="text-gray-400">Shield Amount:</span>
                  <span className={`text-${color}-400 font-semibold`}>
                    {calculation.totalShield.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">HoT per turn:</span>
                  <span className="text-green-400 font-semibold">
                    {calculation.healOverTime.toLocaleString()}
                  </span>
                </div>
              </>
            )}
            
            {skillKey === 'skill3' && (
              <>
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Damage:</span>
                  <span className={`text-${color}-400 font-semibold`}>
                    {calculation.totalDamage.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Taunt Chance:</span>
                  <span className="text-orange-400 font-semibold">
                    {calculation.tauntChance}%
                  </span>
                </div>
              </>
            )}
            
            {skillKey === 'ultimate' && (
              <>
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Damage:</span>
                  <span className={`text-${color}-400 font-semibold`}>
                    {calculation.totalDamage.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Lifesteal:</span>
                  <span className="text-green-400 font-semibold">
                    {calculation.lifesteal.toLocaleString()}
                  </span>
                </div>
              </>
            )}
          </div>
        )}
      </motion.div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Stats Controls */}
      <div className="glass rounded-xl p-6 border border-odd-500/30">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-odd-400" />
          Character Stats
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <StatSlider
            label="HP"
            value={stats.hp}
            onChange={(value) => setStats(prev => ({ ...prev, hp: value }))}
            min={15000}
            max={35000}
            step={100}
            icon={Heart}
            color="red"
          />
          <StatSlider
            label="ATK"
            value={stats.atk}
            onChange={(value) => setStats(prev => ({ ...prev, atk: value }))}
            min={500}
            max={2000}
            step={10}
            icon={Swords}
            color="orange"
          />
          <StatSlider
            label="CRIT Rate (%)"
            value={stats.critRate}
            onChange={(value) => setStats(prev => ({ ...prev, critRate: value }))}
            min={0}
            max={100}
            step={1}
            icon={Star}
            color="yellow"
          />
          <StatSlider
            label="CRIT DMG (%)"
            value={stats.critDmg}
            onChange={(value) => setStats(prev => ({ ...prev, critDmg: value }))}
            min={150}
            max={350}
            step={5}
            icon={TrendingUp}
            color="purple"
          />
        </div>
      </div>

      {/* Battle Conditions */}
      <div className="glass rounded-xl p-6 border border-blue-500/30">
        <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
          <Shield className="w-5 h-5 text-blue-400" />
          Battle Conditions
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <StatSlider
            label="Bloodshed Stacks"
            value={bloodshedStacks}
            onChange={setBloodshedStacks}
            min={0}
            max={20}
            step={1}
            icon={Heart}
            color="red"
          />
          <StatSlider
            label="Current HP (%)"
            value={currentHpPercent}
            onChange={setCurrentHpPercent}
            min={1}
            max={100}
            step={1}
            icon={Heart}
            color="green"
          />
        </div>
      </div>

      {/* Skills */}
      <div className="glass rounded-xl p-6 border border-purple-500/30">
        <h3 className="text-xl font-bold text-white mb-4">Skill Calculations</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <SkillCard
            skillKey="skill1"
            skill={characterData.skills.skill1}
            calculation={calculations.skill1}
            isSelected={selectedSkill === 'skill1'}
            onClick={() => setSelectedSkill('skill1')}
          />
          <SkillCard
            skillKey="skill2"
            skill={characterData.skills.skill2}
            calculation={calculations.skill2}
            isSelected={selectedSkill === 'skill2'}
            onClick={() => setSelectedSkill('skill2')}
          />
          <SkillCard
            skillKey="skill3"
            skill={characterData.skills.skill3}
            calculation={calculations.skill3}
            isSelected={selectedSkill === 'skill3'}
            onClick={() => setSelectedSkill('skill3')}
          />
          <SkillCard
            skillKey="ultimate"
            skill={characterData.skills.ultimateSkill}
            calculation={calculations.ultimate}
            isSelected={selectedSkill === 'ultimate'}
            onClick={() => setSelectedSkill('ultimate')}
          />
        </div>
      </div>
    </div>
  );
};

export default DamageCalculator;
