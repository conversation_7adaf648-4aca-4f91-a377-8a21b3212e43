import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import TiamatCard from './components/TiamatCard';
import CalculatorTabs from './components/CalculatorTabs';
import InteractiveControls from './components/InteractiveControls';
import VisualEffects from './components/ParticleBackground';
import SkillEffects from './components/SkillEffects';
import LoadingScreen from './components/LoadingScreen';
import MobileNavigation from './components/MobileNavigation';
import NotificationSystem from './components/NotificationSystem';
import { tiamatData } from './data/tiamat';

function App() {
  const [selectedCharacter, setSelectedCharacter] = useState(tiamatData);
  const [activeSkillEffect, setActiveSkillEffect] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('damage');

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <AnimatePresence>
        <LoadingScreen onComplete={() => setIsLoading(false)} />
      </AnimatePresence>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700 relative overflow-hidden">
      {/* Advanced Visual Effects */}
      <VisualEffects />

      {/* Mobile Navigation */}
      <MobileNavigation
        activeTab={activeTab}
        onTabChange={setActiveTab}
        characterData={selectedCharacter}
      />

      {/* Skill Effects */}
      <SkillEffects
        skillName={activeSkillEffect}
        isActive={!!activeSkillEffect}
        onComplete={() => setActiveSkillEffect(null)}
      />

      {/* Notification System */}
      <NotificationSystem />

      {/* Header */}
      <motion.header
        className="relative z-10 text-center py-8"
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 1 }}
      >
        <h1 className="text-5xl font-bold gradient-text mb-4">
          Etheria Restart Calculator
        </h1>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          Interactive damage and shield calculator for your favorite characters
        </p>
        <div className="mt-4 flex justify-center gap-4">
          <span className="px-4 py-2 bg-odd-500/20 text-odd-300 rounded-full border border-odd-500/30 text-sm">
            Team Collaboration Ready
          </span>
          <span className="px-4 py-2 bg-blue-500/20 text-blue-300 rounded-full border border-blue-500/30 text-sm">
            Real-time Calculations
          </span>
        </div>
      </motion.header>

      {/* Main content */}
      <main className="relative z-10 container mx-auto px-4 py-8 pt-20 lg:pt-8 pb-20 lg:pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Character Card - Always visible */}
            <motion.div
              className="lg:col-span-4 xl:col-span-3"
              initial={{ opacity: 0, x: -100 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, delay: 0.3 }}
            >
              <div className="space-y-6">
                <TiamatCard
                  characterData={selectedCharacter}
                  onStatsChange={(newStats) => {
                    setSelectedCharacter(prev => ({
                      ...prev,
                      maxStats: { ...prev.maxStats, ...newStats }
                    }));
                  }}
                />

                {/* Interactive Controls - Hidden on mobile, shown on larger screens */}
                <motion.div
                  className="hidden lg:block"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 1, delay: 0.8 }}
                >
                  <InteractiveControls
                    onSave={() => console.log('Save build')}
                    onShare={() => console.log('Share build')}
                    onReset={() => console.log('Reset build')}
                    onExport={() => console.log('Export build')}
                  />
                </motion.div>
              </div>
            </motion.div>

            {/* Calculator Panel */}
            <motion.div
              className="lg:col-span-8 xl:col-span-9"
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              <CalculatorTabs
                characterData={selectedCharacter}
                activeTab={activeTab}
                onTabChange={setActiveTab}
              />
            </motion.div>
          </div>
        </div>
      </main>

      {/* Floating Action Button for Skill Effects */}
      <motion.div
        className="fixed bottom-8 right-8 z-50"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5, delay: 2 }}
      >
        <motion.button
          className="w-16 h-16 bg-gradient-to-br from-odd-500 to-purple-600 rounded-full shadow-2xl flex items-center justify-center text-white text-2xl"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          animate={{
            boxShadow: [
              "0 0 20px rgba(217, 70, 239, 0.5)",
              "0 0 40px rgba(217, 70, 239, 0.8)",
              "0 0 20px rgba(217, 70, 239, 0.5)"
            ]
          }}
          transition={{
            boxShadow: { duration: 2, repeat: Infinity }
          }}
          onClick={() => {
            const skills = ['Thunder Spear', 'Seal of Life', 'Eternity', 'Purge Day'];
            const randomSkill = skills[Math.floor(Math.random() * skills.length)];
            setActiveSkillEffect(randomSkill);
          }}
        >
          ⚡
        </motion.button>
      </motion.div>

      {/* Footer */}
      <motion.footer
        className="relative z-10 text-center py-8 mt-16"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 1 }}
      >
        <p className="text-gray-400">
          Built for team collaboration • Real-time calculations • Interactive design
        </p>
        <motion.p
          className="text-sm text-odd-400 mt-2"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          Click the ⚡ button to see skill effects!
        </motion.p>
      </motion.footer>
    </div>
  );
}

export default App;
